name: Betanet Spec Compliance Check

# This workflow runs the Betanet specification linter against a built
# Betanet node implementation.  Copy this file to
# `.github/workflows/compliance.yaml` in your project and adjust the build
# and binary paths as needed.

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]


jobs:
  compliance:
    runs-on: ubuntu-latest
    permissions:

    
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      # --- Build your Betanet implementation -------------------------------
      - name: Build Betanet binary
        run: |
          # TODO: replace with commands that build your Betanet node
          echo "Build steps go here"
          # Example:
          # cargo build --release
          # resulting binary at ./target/release/betanet-node

      # --- Run the compliance linter --------------------------------------
      - name: Download Betanet compliance linter
        run: |
          curl -L -o betalint.py https://raw.githubusercontent.com/Mentor1337/Betalint/main/Betalint.py
          chmod +x betalint.py

      - name: Run compliance check
        run: |
          # Update --binary path to point at the built Betanet binary
          python betalint.py \
            --binary ./path/to/betanet-node \
            --output json \
            --report-file compliance-report.json \
            --sbom-file sbom.json

      - name: Upload compliance report
        uses: actions/upload-artifact@v3
        with:
          name: betanet-compliance-report
          path: |
            compliance-report.json
            sbom.json

      - name: Comment results on pull requests
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const report = JSON.parse(fs.readFileSync('compliance-report.json', 'utf8'));
            const passed = report.passed;
            const total = report.total_requirements;
            const score = Math.round((passed / total) * 100);
            const comment = `## 🔍 Betanet Spec Compliance\n\n**Score:** ${score}% (${passed}/${total})\n- ✅ Passed: ${report.passed}\n- ❌ Failed: ${report.failed}\n- ⚠️ Warnings: ${report.warnings}\n\nFull report available in workflow artifacts.`;
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          